<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import { getWarehouseData, warehouseUpdate } from '#/api/maintenance/data';

const emit = defineEmits(['success']);

const data = ref();

const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        autocomplete: 'off',
        maxlength: 8,
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'WarehouseCode',
      help: '仓库代码创建后无法修改',
      // 界面显示的label
      label: '仓库代码',
      rules: z.string().min(4, { message: '仓库代码至少包含4个字符' }),
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'WarehouseName',
      label: '仓库名称',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '启用',
            value: true,
          },
          {
            label: '停用',
            value: false,
          },
        ],
      },
      defaultValue: true,
      fieldName: 'IsActived',
      label: '启用状态',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'Address',
      label: '仓库地址',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'ContactPerson',
      label: '联系人',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'Telephone',
      label: '联系电话',
    },

    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'Remark',
      label: '备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});
const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    /* console.log(baseFormApi.validate()); */
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      baseFormApi.updateSchema([
        {
          componentProps: {
            disabled: !(data.value.id === 'new'),
          },
          fieldName: 'WarehouseCode',
        },
      ]);
      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getWarehouseData(id)
    .then((res) => {
      if (data.value.id === 'new') {
        drawerApi.setState({ title: '新增仓库' });
        baseFormApi.resetForm();
      } else {
        drawerApi.setState({ title: '编辑仓库' });
        baseFormApi.setValues(res);
      }
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    warehouseUpdate(Object.assign({ id: data.value.id }, values))
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else />
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
