<script lang="ts" setup>
import { ref } from 'vue';

import { CodeEditor, Fallback, useVbenDrawer } from '@vben/common-ui';

import { Card, Descriptions, message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { getAPIData } from '#/api/maintenance/interface';
import { neoApiCall } from '#/api/maintenance/manual';

const data = ref();
const apiData = ref({}) as any;
const apiParams = ref('');
const codeData = ref('');
const isNotFound = ref(false);
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'APIMethod',
      label: 'APIMethod',
      dependencies: {
        show: false,
        triggerFields: ['X'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'URLParams',
      label: 'URLParams',
      dependencies: {
        show: false,
        triggerFields: ['X'],
      },
    },
    {
      component: 'Input',
      dependencies: {
        show(values) {
          return values.URLParams;
        },
        triggerFields: ['URLParams'],
      },
      fieldName: 'RequestParams',
      help: '请参照URL参数填写，多个参数的值用半角逗号隔开（每行调用1次API,单次最多提交100行）',
      label: '请求参数',
      rules: 'required',
    },
    {
      component: 'Input',
      dependencies: {
        show(values) {
          return values.APIMethod !== 'GET';
        },
        triggerFields: ['APIMethod'],
      },
      fieldName: 'RequestBody',
      label: '请求内容',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});
const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  class: 'w-[600px]',
  closeOnClickModal: false,
  onCancel() {
    drawerApi.close();
  },
  onConfirm: async () => {
    isNotFound.value = false;
    await formApi.submitForm();
    /* drawerApi.close(); */
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      apiParams.value = '';
      codeData.value = '';
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
});

function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getAPIData(id)
    .then((res) => {
      apiData.value = res;
      const paramsMatches = res.Setting.APIURL.match(/\{(\d+)\}/g);
      apiParams.value = paramsMatches ? paramsMatches.join(',') : '';
      formApi.setValues({
        APIMethod: res.Setting.APIMethod,
        URLParams: apiParams.value,
      });
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    const trimmedStr = values.RequestParams.replaceAll(/^\s+|\s+$/g, '');
    if (trimmedStr === '') {
      message.error(`请输入至少1个请求参数`);
      return;
    }
    if (trimmedStr.split(/\n/).length > 100) {
      message.error(`单次最多提交100行请求参数！`);
      return;
    }
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    neoApiCall(Object.assign({ id: data.value.id }, values))
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer :title="`${apiData.Name} - API调用`">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Descriptions
      v-else
      :column="1"
      bordered
      layout="vertical"
      size="small"
      style="padding-bottom: 8px"
    >
      <Descriptions.Item label="调用URL">
        {{ apiData.Setting?.APIURL }}
      </Descriptions.Item>
      <Descriptions.Item v-if="apiParams" label="URL参数">
        {{ apiParams }}
      </Descriptions.Item>
    </Descriptions>
    <Card :can-expan="false" size="small">
      <!-- <template #title>
        {{ 'URL参数' }}
      </template> -->
      <!-- <template #extra><a @click="handleCopy">Copy</a></template> -->
      <div class="neoapi-code">
        <!-- <CodeEditor v-model:value="codeData" class="h-full" /> -->
      </div>
      <Form>
        <template #RequestParams="slotProps">
          <CodeEditor
            v-if="apiData.Setting?.APIMethod === 'GET'"
            v-bind="slotProps"
            class="neoapi-code w-full rounded-md border border-gray-300 px-1 py-1"
          />
          <a-input
            v-else
            v-bind="slotProps"
            :maxlength="64"
            autocomplete="off"
          />
        </template>
        <template #RequestBody="slotProps">
          <CodeEditor
            v-bind="slotProps"
            class="neoapi-code w-full rounded-md border border-gray-300 px-1 py-1"
          />
        </template>
      </Form>
    </Card>
  </Drawer>
</template>

<style lang="scss">
.neoapi-code .CodeMirror {
  height: auto !important;
}
</style>
