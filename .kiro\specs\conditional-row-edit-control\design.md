# 设计文档

## 概述

本设计文档描述了如何在VxeGrid表格中实现基于ReceivingStatus状态的条件行编辑控制功能。当某行的ReceivingStatus字段为true（已收货状态）时，该行的SSCC和PalletNo字段将被禁止编辑，同时提供相应的视觉反馈。

## 架构

### 核心设计原则

1. **最小侵入性**: 在现有VxeGrid配置基础上进行扩展，不破坏现有功能
2. **条件控制**: 基于行数据的ReceivingStatus字段动态控制编辑权限
3. **视觉反馈**: 为禁用状态提供清晰的视觉提示
4. **性能优化**: 避免不必要的重新渲染和计算

### 技术架构

```
VxeGrid配置层
├── editRender配置扩展
├── cellStyle样式控制
└── beforeEditMethod编辑拦截

条件控制逻辑层
├── 字段级别编辑权限检查
├── 行状态判断逻辑
└── 视觉样式应用

数据层
└── ReceivingStatus状态字段
```

## 组件和接口

### 1. VxeGrid列配置扩展

#### SSCC字段配置

```typescript
{
  field: 'SSCC',
  title: 'SSCC',
  width: 144,
  editRender: {
    name: 'AInput',
    autofocus: '',
    props: {
      size: 'small',
      autocomplete: 'off',
      maxlength: 20
    },
  },
}
```

#### PalletNo字段配置

```typescript
{
  field: 'PalletNo',
  title: '入库托盘号',
  width: 88,
  editRender: {
    name: 'AInput',
    autofocus: '',
    props: {
      size: 'small',
      autocomplete: 'off',
      maxlength: 20,
    },
  },
}
```

### 2. 编辑控制接口

#### beforeEditMethod扩展

```typescript
interface EditControlParams {
  row: any;
  column: any;
  rowIndex: number;
  columnIndex: number;
}

function beforeEditMethod(params: EditControlParams): boolean {
  const { row, column } = params;

  // 现有的isClosed检查
  if (props.isClosed) {
    return false;
  }

  // 新增的条件编辑控制
  if (row.ReceivingStatus && ['SSCC', 'PalletNo'].includes(column.field)) {
    return false;
  }

  return true;
}
```

### 3. 样式控制接口

#### cellStyle扩展

```typescript
interface CellStyleParams {
  row: any;
  column: any;
  rowIndex: number;
  columnIndex: number;
}

function cellStyle(params: CellStyleParams): CSSProperties | null {
  const { row, column } = params;

  // 现有样式逻辑保持不变
  // ...existing style logic...

  // 新增禁用字段样式
  if (row.ReceivingStatus && ['SSCC', 'PalletNo'].includes(column.field)) {
    return {
      backgroundColor: '#f5f5f5',
      color: '#00000040',
      cursor: 'not-allowed',
    };
  }

  return null;
}
```

## 数据模型

### 行数据结构

```typescript
interface RowData {
  ReceivingStatus: boolean; // 收货状态：true=已收货，false=待收货
  SSCC: string; // SSCC编号
  PalletNo: string; // 入库托盘号
  // ...其他字段
}
```

### 编辑状态模型

```typescript
interface EditState {
  isEditable: boolean; // 字段是否可编辑
  isDisabled: boolean; // 字段是否禁用
  disabledReason: string; // 禁用原因
}
```

## 错误处理

### 1. 编辑拦截处理

- 当用户尝试编辑已收货行的SSCC或PalletNo字段时，阻止进入编辑模式
- 不显示错误消息，通过视觉样式提示用户该字段不可编辑

### 2. 数据验证

- 确保ReceivingStatus字段存在且为布尔类型
- 处理undefined或null值的情况，默认视为false（待收货）

### 3. 兼容性处理

- 保持与现有isClosed逻辑的兼容性
- 确保其他字段的编辑功能不受影响

## 测试策略

### 1. 单元测试

- 测试beforeEditMethod在不同ReceivingStatus状态下的返回值
- 测试cellStyle在不同条件下的样式返回
- 测试字段编辑权限的正确性

### 2. 集成测试

- 测试与现有VxeGrid功能的集成
- 测试与isClosed状态的交互
- 测试用户交互流程的完整性

### 3. 用户体验测试

- 验证视觉反馈的清晰度
- 测试鼠标悬停效果
- 验证编辑流程的直观性

### 4. 性能测试

- 测试大量数据下的渲染性能
- 验证条件判断逻辑的执行效率
- 确保不影响现有表格性能

## 实现细节

### 1. 条件判断优化

```typescript
// 使用缓存避免重复计算
const isFieldDisabled = (row: any, field: string): boolean => {
  return row.ReceivingStatus === true && ['SSCC', 'PalletNo'].includes(field);
};
```

### 2. 样式常量定义

```typescript
const DISABLED_CELL_STYLE = {
  backgroundColor: '#f5f5f5',
  color: '#00000040',
  cursor: 'not-allowed',
} as const;
```

### 3. 字段配置统一管理

```typescript
const CONDITIONAL_EDIT_FIELDS = ['SSCC', 'PalletNo'] as const;
```

## 扩展性考虑

### 1. 配置化支持

- 支持通过配置指定需要条件控制的字段
- 支持自定义禁用条件逻辑
- 支持自定义禁用样式

### 2. 多条件支持

- 预留接口支持多个条件的组合判断
- 支持不同字段使用不同的禁用条件

### 3. 国际化支持

- 为禁用提示信息预留国际化接口
- 支持不同语言环境下的用户体验
