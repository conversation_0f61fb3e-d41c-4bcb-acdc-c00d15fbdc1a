<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import { accountUpdate, getAccountData } from '#/api/maintenance/user';

const emit = defineEmits(['success']);

const data = ref();

const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'UserID',
      help: '用户ID创建后无法修改',
      // 界面显示的label
      label: '用户ID',
      rules: z.string().min(6, { message: '用户ID至少包含6个字符' }),
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'UserName',
      label: '用户名称',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        filterOption: true,
        maxTagCount: 3,
        mode: 'multiple',
        placeholder: '请选择',
      },
      fieldName: 'UserRole',
      label: '用户角色',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 11,
        placeholder: '请输入',
      },
      fieldName: 'MobileNum',
      label: '手机号码',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 64,
        placeholder: '请输入',
      },
      fieldName: 'Email',
      label: '电子邮件',
      rules: z
        .union([z.string().email('请输入正确的邮箱'), z.string().trim().max(0)])
        .optional(),
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'Remark',
      label: '备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});
const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    /* console.log(baseFormApi.validate()); */
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      baseFormApi.updateSchema([
        {
          componentProps: {
            disabled: !(data.value.id === 'new'),
          },
          fieldName: 'UserID',
        },
      ]);
      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getAccountData(id)
    .then((res) => {
      if (data.value.id === 'new') {
        drawerApi.setState({ title: '新增用户' });
        baseFormApi.resetForm();
      } else {
        drawerApi.setState({ title: '编辑用户' });
        baseFormApi.setValues(res.info);
      }
      baseFormApi.updateSchema([
        {
          componentProps: {
            options: res.roleOptions,
          },
          fieldName: 'UserRole',
        },
      ]);
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    accountUpdate(Object.assign({ id: data.value.id }, values))
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else />
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
