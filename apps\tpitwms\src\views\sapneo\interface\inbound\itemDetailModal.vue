<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';

const data = ref();

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: false,
  fullscreenButton: true,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const modalData = modalApi.getData<Record<string, any>>();
      data.value = modalData;
      handleItemData(modalData?.itemData || []);
      if (modalData?.itemData?.length > 0) {
        itemGridApi.grid?.setAllTreeExpand(true);
      }
      // 动态设置标题
      if (modalData?.deliveryNumber) {
        modalApi.setState({
          title: `Inbound - ${modalData.deliveryNumber}`,
        });
      }
    }
  },
  title: 'Inbound',
});

const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    { field: 'ItemNumber', title: '项号', width: 100, treeNode: true },
    { field: 'Plant', title: '工厂编码', width: 72 },
    { field: 'StorageLocation', title: '库存地点', width: 72 },
    {
      field: 'MaterialCode',
      title: '产品编号',
      width: 100,
      slots: { default: 'skucode' },
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    { field: 'InvoiceNumber', title: '发票号', minWidth: 80 },
    { field: 'Qty', title: '件数', minWidth: 72 },
    {
      field: 'BatchNumber',
      title: '批次',
      width: 96,
      slots: { default: 'batch' },
    },
    {
      field: 'BatchStatus',
      title: '批次限制',
      width: 72,
      slots: { default: 'batchstatus' },
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 88,
      formatter: 'formatDate',
    },
    { field: 'SsccId', title: 'SsccId', minWidth: 144 },
    { field: 'LowerLevelSsccId', title: 'LowerLevelSsccId', minWidth: 144 },
    { field: 'PackId', title: '包装材料', width: 72 },
    {
      field: 'StockTypeName',
      title: '库存类型',
      width: 72,
    },
    { field: 'MovementType', title: '移动类型', width: 72 },
    { field: 'LorealDocument', title: '采购订单', minWidth: 88 },
    { field: 'POItemNumber', title: '采购项号', minWidth: 72 },
    { field: 'StoType', title: '采购类型', width: 72 },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  stripe: false,
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
  },
  height: auto,
});

const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});

function handleItemData(itemData: any) {
  itemGridApi.setGridOptions({
    data: itemData,
  });
}

// 暴露给父组件使用的方法
defineExpose({
  openSkuDrawer: (id: string) => {
    // 这里需要从父组件传递过来
    data.value?.openSkuDrawer?.(id);
  },
  openBatchDrawer: (sku: string, batch: string) => {
    // 这里需要从父组件传递过来
    data.value?.openBatchDrawer?.(sku, batch);
  },
});
</script>

<template>
  <Modal>
    <div class="h-full">
      <ItemGrid>
        <template #skucode="{ row }">
          <a-button
            size="small"
            type="link"
            @click="data?.openSkuDrawer?.(row.MaterialCode)"
          >
            {{ row.MaterialCode }}
          </a-button>
        </template>
        <template #batch="{ row }">
          <a-button
            v-if="row.BatchNumber"
            size="small"
            type="link"
            @click="data?.openBatchDrawer?.(row.MaterialCode, row.BatchNumber)"
          >
            {{ row.BatchNumber }}
          </a-button>
        </template>
        <template #batchmanage="{ row }">
          <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
            {{ row.isBatchManagementRequired ? '是' : '否' }}
          </Tag>
        </template>
        <template #batchstatus="{ row }">
          <Tag :color="row.BatchStatus ? 'red' : 'green'">
            {{ row.BatchStatus ? '是' : '否' }}
          </Tag>
        </template>
      </ItemGrid>
    </div>
  </Modal>
</template>
