<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getInboundItem, getInboundList } from '#/api/sapneo';

import batchDrawer from '../../master/batch/batchDrawer.vue';
import customerDrawer from '../../master/customer/customerDrawer.vue';
import skuDrawer from '../../master/product/skuDrawer.vue';

const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const searchField: any = reactive({
  DeliveryNumber: undefined,
  /* LorealDocument: undefined,
  WMSDoc: undefined, */
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    { field: 'ItemNumber', title: '项号', width: 100, treeNode: true },
    { field: 'Plant', title: '工厂编码', width: 72 },
    { field: 'StorageLocation', title: '库存地点', width: 72 },
    {
      field: 'MaterialCode',
      title: '产品编号',
      width: 100,
      slots: { default: 'skucode' },
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    { field: 'InvoiceNumber', title: '发票号', minWidth: 80 },
    { field: 'Qty', title: '件数', minWidth: 72 },
    {
      field: 'BatchNumber',
      title: '批次',
      width: 96,
      slots: { default: 'batch' },
    },
    {
      field: 'BatchStatus',
      title: '批次限制',
      width: 72,
      slots: { default: 'batchstatus' },
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 88,
      formatter: 'formatDate',
    },
    { field: 'SsccId', title: 'SsccId', minWidth: 144 },
    { field: 'LowerLevelSsccId', title: 'LowerLevelSsccId', minWidth: 144 },
    { field: 'PackId', title: '包装材料', width: 72 },
    {
      field: 'StockTypeName',
      title: '库存类型',
      width: 72,
      /* slots: { default: 'stocktype' }, */
    },
    { field: 'MovementType', title: '移动类型', width: 72 },
    { field: 'LorealDocument', title: '采购订单', minWidth: 88 },
    { field: 'POItemNumber', title: '采购项号', minWidth: 72 },
    { field: 'StoType', title: '采购类型', width: 72 },
    /* { field: 'BatchBySupplier', title: '供应商批次', width: 84 }, */
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  /* rowConfig: {
    useKey: true,
    keyField: 'Guid',
  }, */
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  stripe: false,
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
  },
  /* virtualYConfig: {
    enabled: true,
    gt: 0,
  },
  maxHeight: 1200, */
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'NEOInboundDelivery',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'DeliveryNumber',
      title: 'Inbound',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'AsnNumber',
      title: 'ASN编号',
      minWidth: 108,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CompanyCode',
      title: '货主代码',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'DeliveryType',
      title: '单据类型',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'LorealDocument',
      title: '采购订单',
      minWidth: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Supplier',
      title: '供应商',
      minWidth: 96,
      slots: { default: 'supplier' },
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ShippingDate',
      title: '发货日期',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'DeliveryDate',
      title: '计划送达',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'ItemsCount',
      title: 'Item项',
      minWidth: 60,
    },
    {
      field: 'SSCCCount',
      title: 'SSCC数量',
      minWidth: 76,
    },
    {
      field: 'WMSDoc',
      title: 'WMS入库订单',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'LastGRDate',
      title: '最近GR日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'IsGRClosed',
      title: '关闭状态',
      minWidth: 88,
      filters: [
        { label: '已关闭', value: true },
        { label: '未关闭', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isGRClosed' },
    },
    {
      field: 'CreateDate',
      title: 'W创建日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: 'W更新日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'TransportationIdCode',
      title: '原始IBD',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'HBLInLISInvoice',
      title: 'LISHBL',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
  ],
  expandConfig: {
    accordion: true,
    /* height: 320, */
    mode: 'inside',
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getInboundList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollX: { enabled: false },
  scrollY: { enabled: false }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getInboundItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.DeliveryNumber"
            allow-clear
            class="mr-2 w-60"
            placeholder="Inbound"
            @search="searchEvent('DeliveryNumber')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode="{ row }">
              <a-button
                size="small"
                type="link"
                @click="openSkuDrawer(row.MaterialCode)"
              >
                {{ row.MaterialCode }}
              </a-button>
            </template>
            <template #batch="{ row }">
              <a-button
                v-if="row.BatchNumber"
                size="small"
                type="link"
                @click="openBatchDrawer(row.MaterialCode, row.BatchNumber)"
              >
                {{ row.BatchNumber }}
              </a-button>
            </template>
            <template #batchmanage="{ row }">
              <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
                {{ row.isBatchManagementRequired ? '是' : '否' }}
              </Tag>
            </template>
            <template #batchstatus="{ row }">
              <Tag :color="row.BatchStatus ? 'red' : 'green'">
                {{ row.BatchStatus ? '是' : '否' }}
              </Tag>
            </template>
          </ItemGrid>
        </div>
      </template>
      <template #supplier="{ row }">
        <a
          v-if="row.Supplier"
          class="text-blue-500"
          @click="openCustomerDrawer(row.Supplier)"
        >
          {{ row.Supplier }}
        </a>
      </template>
      <template #isGRClosed="{ row }">
        <Tag :color="row.IsGRClosed ? 'green' : 'blue'">
          {{ row.IsGRClosed ? '已关闭' : '未关闭' }}
        </Tag>
      </template>
    </Grid>
    <CustomerDrawer />
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
