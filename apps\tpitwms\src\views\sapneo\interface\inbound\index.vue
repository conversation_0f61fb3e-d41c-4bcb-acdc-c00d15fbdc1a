<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getInboundItem, getInboundList } from '#/api/sapneo';

import batchDrawer from '../../master/batch/batchDrawer.vue';
import customerDrawer from '../../master/customer/customerDrawer.vue';
import skuDrawer from '../../master/product/skuDrawer.vue';
import itemDetailModal from './itemDetailModal.vue';

const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
const [ItemDetailModal, itemDetailModalApi] = useVbenModal({
  connectedComponent: itemDetailModal,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
async function openItemDetailModal(row: any) {
  const itemData = await getInboundItem(row.Guid);
  itemDetailModalApi.setData({
    itemData,
    openSkuDrawer,
    openBatchDrawer,
    deliveryNumber: row.DeliveryNumber,
  });
  itemDetailModalApi.open();
}
const searchField: any = reactive({
  DeliveryNumber: undefined,
  /* LorealDocument: undefined,
  WMSDoc: undefined, */
});

const gridOptions = reactive<VxeGridProps>({
  id: 'NEOInboundDelivery',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      title: '操作',
      width: 80,
      slots: { default: 'action' },
    },
    {
      field: 'DeliveryNumber',
      title: 'Inbound',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'AsnNumber',
      title: 'ASN编号',
      minWidth: 108,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CompanyCode',
      title: '货主代码',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'DeliveryType',
      title: '单据类型',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'LorealDocument',
      title: '采购订单',
      minWidth: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Supplier',
      title: '供应商',
      minWidth: 96,
      slots: { default: 'supplier' },
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ShippingDate',
      title: '发货日期',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'DeliveryDate',
      title: '计划送达',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'ItemsCount',
      title: 'Item项',
      minWidth: 60,
    },
    {
      field: 'SSCCCount',
      title: 'SSCC数量',
      minWidth: 76,
    },
    {
      field: 'WMSDoc',
      title: 'WMS入库订单',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'LastGRDate',
      title: '最近GR日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'IsGRClosed',
      title: '关闭状态',
      minWidth: 88,
      filters: [
        { label: '已关闭', value: true },
        { label: '未关闭', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isGRClosed' },
    },
    {
      field: 'CreateDate',
      title: 'W创建日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: 'W更新日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'TransportationIdCode',
      title: '原始IBD',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'HBLInLISInvoice',
      title: 'LISHBL',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getInboundList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollX: { enabled: false },
  scrollY: { enabled: false }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.DeliveryNumber"
            allow-clear
            class="mr-2 w-60"
            placeholder="Inbound"
            @search="searchEvent('DeliveryNumber')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #action="{ row }">
        <a-button size="small" type="link" @click="openItemDetailModal(row)">
          查看
        </a-button>
      </template>
      <template #supplier="{ row }">
        <a
          v-if="row.Supplier"
          class="text-blue-500"
          @click="openCustomerDrawer(row.Supplier)"
        >
          {{ row.Supplier }}
        </a>
      </template>
      <template #isGRClosed="{ row }">
        <Tag :color="row.IsGRClosed ? 'green' : 'blue'">
          {{ row.IsGRClosed ? '已关闭' : '未关闭' }}
        </Tag>
      </template>
    </Grid>
    <CustomerDrawer />
    <SkuDrawer />
    <BatchDrawer />
    <ItemDetailModal />
  </Page>
</template>
