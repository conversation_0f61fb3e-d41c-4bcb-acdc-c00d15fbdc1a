<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import { dictionaryUpdate, getDictionaryData } from '#/api/maintenance/data';

const emit = defineEmits(['success']);

const data = ref();

const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'DictionaryTypeName',
      label: '选项类型',
      rules: 'required',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 32,
        placeholder: '请输入',
      },
      fieldName: 'DictionaryName',
      label: '选项名称',
      rules: z.string().min(1, { message: '选项名称至少包含1个字符' }),
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 32,
        placeholder: '请输入',
      },
      fieldName: 'DictionaryValue',
      label: '选项值',
      rules: z.string().min(1, { message: '选项值至少包含1个字符' }),
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 3,
        min: 0,
        placeholder: '请输入排序',
      },
      fieldName: 'SortBy',
      defaultValue: 0,
      rules: 'required',
      label: '排序数字',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '启用',
            value: true,
          },
          {
            label: '停用',
            value: false,
          },
        ],
      },
      defaultValue: true,
      fieldName: 'IsActived',
      label: '启用状态',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 32,
        placeholder: '请输入',
      },
      fieldName: 'DictionaryValueExt',
      label: '扩展属性',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      defaultValue: false,
      fieldName: 'IsDefault',
      label: '是否默认',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});
const [Drawer, drawerApi] = useVbenDrawer({
  destroyOnClose: true,
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    /* console.log(baseFormApi.validate()); */
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getDictionaryData(id)
    .then((res) => {
      if (data.value.id === 'new') {
        drawerApi.setState({ title: '新增选项' });
        baseFormApi.resetForm();
      } else {
        drawerApi.setState({ title: '编辑选项' });
        baseFormApi.setValues(res);
      }
      baseFormApi.setFieldValue(
        'DictionaryTypeName',
        data.value.typItem?.label,
      );
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    dictionaryUpdate(
      Object.assign(
        { id: data.value.id, DictionaryType: data.value.typItem.value },
        values,
      ),
    )
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else />
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
