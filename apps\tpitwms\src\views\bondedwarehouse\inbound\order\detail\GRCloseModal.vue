<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal, VbenLoading } from '@vben/common-ui';

import { Descriptions, message, Result, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenForm } from '#/adapter';
import {
  bwInOrderInboundGR,
  bwInOrderInboundGRCheck,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);

const data = ref();
const isTaskCreated = ref(false);
const loadingRef = ref(false);
const movementID = ref(undefined);
const inboundData = ref({}) as any;
const router = useRouter();
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PostingDate',
      label: '记账日期',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isTaskCreated.value = false;
      loadingRef.value = true;
      movementID.value = undefined;
      modalApi.setState({ showConfirmButton: false });
      inboundData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      formApi.setValues({ PostingDate: dayjs().format() });
      fetch();
    }
  },
  title: 'GR关闭确认',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    bwInOrderInboundGR({
      id: data.value.id,
      date: values.PostingDate,
      isClose: true,
      ids: [],
    })
      .then((res) => {
        modalApi.setState({ showCancelButton: false });
        modalApi.setState({ showConfirmButton: false });
        isTaskCreated.value = true;
        movementID.value = res;
        message.success('GR任务创建成功！');
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}

function handleClose() {
  modalApi.close();
  emit('success');
}
function handleGoDetail() {
  modalApi.close();
  router.push({
    name: 'BWStockMovementDetail',
    params: { id: movementID.value },
  });
  emit('success');
}
async function fetch() {
  await bwInOrderInboundGRCheck({ id: data.value.id, isClose: true, ids: [] })
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      inboundData.value = res;
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>
<template>
  <BaseModal>
    <Result v-if="isTaskCreated" status="success" title="GR回传任务创建成功">
      <template #extra>
        <a-button
          v-if="movementID && movementID !== ''"
          type="primary"
          @click="handleGoDetail()"
        >
          查看库存调整单据
        </a-button>
        <a-button @click="handleClose()">关闭</a-button>
      </template>
    </Result>
    <div v-else>
      <Form />
      <Descriptions :column="{ xs: 1, sm: 2 }" bordered size="small">
        <Descriptions.Item label="Inbound">
          {{ inboundData.DeliveryNumber }}
        </Descriptions.Item>
        <Descriptions.Item label="产品数量">
          {{ inboundData.TotalQty }}
        </Descriptions.Item>
        <Descriptions.Item label="已GR数量">
          {{ inboundData.QtyIsGR }}
        </Descriptions.Item>
        <Descriptions.Item label="实收总数">
          {{ inboundData.ActualQty }}
        </Descriptions.Item>
        <Descriptions.Item label="首次收货时间">
          {{ inboundData.FirstReceivedDate }}
        </Descriptions.Item>
        <Descriptions.Item label="末次收货时间">
          {{ inboundData.LastReceivedDate }}
        </Descriptions.Item>
        <Descriptions.Item label="收货完整度校验">
          <Tag
            v-if="!loadingRef"
            :bordered="false"
            :color="inboundData.UnReceivedCount === 0 ? 'green' : 'orange'"
          >
            {{ inboundData.UnReceivedCount === 0 ? '通过' : '未通过' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="批次数据校验">
          <Tag
            v-if="!loadingRef"
            :bordered="false"
            :color="inboundData.Is344Required ? 'red' : 'green'"
          >
            {{
              inboundData.Is344Required > 0
                ? '未通过，请分批GR'
                : '通过，主数据存在'
            }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="触发批次调整">
          <Tag
            v-if="!loadingRef"
            :bordered="false"
            :color="inboundData.Is301Required ? 'orange' : 'blue'"
          >
            {{ inboundData.Is301Required > 0 ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>
      </Descriptions>
      <VbenLoading :spinning="loadingRef" />
    </div>
  </BaseModal>
</template>
